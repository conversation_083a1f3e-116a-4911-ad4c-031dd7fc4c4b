import { useState, useRef } from 'react';
import type { ChangeEvent, FormEvent } from 'react';
import { FiUpload, FiSave, FiX } from 'react-icons/fi';
import '../layout/backgroundStyles.css';
import type { Member } from '../MemberCard';

interface ProfileEditorProps {
  initialData?: Partial<Member>;
  onSave: (profileData: FormData) => void;
  onCancel: () => void;
}

const ProfileEditor = ({ initialData = {}, onSave, onCancel }: ProfileEditorProps) => {
  const [profileData, setProfileData] = useState({
    name: initialData.name || '',
    graduation_year: initialData.graduation_year?.toString() || '',
    profession: initialData.profession || '',
    email: initialData.email || '',
    linkedin: initialData.linkedin || '',
    location: initialData.location || '',
    bio: initialData.bio || '',
  });
  
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(initialData.avatar_url || null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };
  
  const handleImageChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
      if (!validTypes.includes(file.type)) {
        setErrors(prev => ({ ...prev, image: 'Please upload a JPG or PNG image' }));
        return;
      }
      
      // Validate file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        setErrors(prev => ({ ...prev, image: 'Image must be smaller than 2MB' }));
        return;
      }
      
      setImageFile(file);
      
      // Create a preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
      
      // Clear error if exists
      if (errors.image) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.image;
          return newErrors;
        });
      }
    }
  };
  
  const triggerImageUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  const removeImage = () => {
    setImageFile(null);
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!profileData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!profileData.graduation_year.trim()) {
      newErrors.graduation_year = 'Graduation year is required';
    } else if (!/^\d{4}$/.test(profileData.graduation_year)) {
      newErrors.graduation_year = 'Please enter a valid 4-digit year';
    }
    
    if (!profileData.profession.trim()) {
      newErrors.profession = 'Profession is required';
    }
    
    if (profileData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(profileData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    if (profileData.linkedin && !profileData.linkedin.includes('linkedin.com')) {
      newErrors.linkedin = 'Please enter a valid LinkedIn URL';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      // Prepare data for submission
      const formData = new FormData();
      
      // Add text fields
      Object.entries(profileData).forEach(([key, value]) => {
        formData.append(key, value);
      });
      
      // Add image if exists
      if (imageFile) {
        formData.append('avatar', imageFile);
      }
      
      // Call the onSave function with the form data
      onSave(formData);
    }
  };
  
  return (
    <div className="bg-white rounded-xl shadow-md p-6 border border-gray-200 profile-editor-container">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-display font-bold text-gray-900">
          {initialData.name ? 'Edit Your Profile' : 'Create Your Profile'}
        </h2>
        <button 
          onClick={onCancel}
          className="text-gray-500 hover:text-gray-700"
          aria-label="Cancel"
        >
          <FiX size={24} />
        </button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Profile Image */}
        <div className="flex flex-col items-center mb-8">
          <div 
            className="w-32 h-32 mb-4 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center relative profile-image-container"
          >
            {imagePreview ? (
              <>
                <img 
                  src={imagePreview} 
                  alt="Profile preview" 
                  className="w-full h-full object-cover"
                />
                <button 
                  type="button"
                  onClick={removeImage}
                  className="absolute bottom-0 right-0 bg-crimson-700 text-white p-1 rounded-full"
                  aria-label="Remove image"
                >
                  <FiX size={16} />
                </button>
              </>
            ) : (
              <div className="text-center text-gray-400">
                <FiUpload size={24} className="mx-auto mb-2" />
                <span className="text-sm">Add Photo</span>
              </div>
            )}
          </div>
          
          <input 
            type="file" 
            ref={fileInputRef} 
            onChange={handleImageChange} 
            className="hidden" 
            accept="image/png, image/jpeg, image/jpg"
          />
          
          <button
            type="button"
            onClick={triggerImageUpload}
            className="btn btn-outline border-crimson-700 text-crimson-700 text-sm hover:bg-crimson-50"
          >
            Upload Professional Headshot
          </button>
          
          {errors.image && (
            <p className="text-red-500 text-sm mt-1">{errors.image}</p>
          )}
          <p className="text-gray-500 text-xs mt-2">
            Upload a professional headshot (JPG or PNG, max 2MB)
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="form-section">
            <h3 className="font-bold text-gray-900 mb-4 pb-2 border-b border-gray-200">
              Basic Information
            </h3>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name*
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={profileData.name}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-crimson-500 focus:border-transparent form-input"
                  placeholder="Enter your full name"
                  required
                />
                {errors.name && (
                  <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                )}
              </div>

              <div>
                <label htmlFor="graduation_year" className="block text-sm font-medium text-gray-700 mb-1">
                  Graduation Year*
                </label>
                <input
                  type="text"
                  id="graduation_year"
                  name="graduation_year"
                  value={profileData.graduation_year}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-crimson-500 focus:border-transparent form-input"
                  placeholder="YYYY"
                  required
                />
                {errors.graduation_year && (
                  <p className="text-red-500 text-sm mt-1">{errors.graduation_year}</p>
                )}
              </div>

              <div>
                <label htmlFor="profession" className="block text-sm font-medium text-gray-700 mb-1">
                  Profession*
                </label>
                <input
                  type="text"
                  id="profession"
                  name="profession"
                  value={profileData.profession}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-crimson-500 focus:border-transparent form-input"
                  placeholder="Enter your current profession"
                  required
                />
                {errors.profession && (
                  <p className="text-red-500 text-sm mt-1">{errors.profession}</p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="form-section">
            <h3 className="font-bold text-gray-900 mb-4 pb-2 border-b border-gray-200">
              Contact Information
            </h3>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address*
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={profileData.email}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-crimson-500 focus:border-transparent form-input"
                  placeholder="Enter your email address"
                  required
                />
                {errors.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                )}
              </div>

              <div>
                <label htmlFor="linkedin" className="block text-sm font-medium text-gray-700 mb-1">
                  LinkedIn Profile
                </label>
                <input
                  type="url"
                  id="linkedin"
                  name="linkedin"
                  value={profileData.linkedin}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-crimson-500 focus:border-transparent form-input"
                  placeholder="https://linkedin.com/in/your-profile"
                />
                {errors.linkedin && (
                  <p className="text-red-500 text-sm mt-1">{errors.linkedin}</p>
                )}
              </div>

              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  value={profileData.location}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-crimson-500 focus:border-transparent form-input"
                  placeholder="City, State"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Bio Section */}
        <div className="form-section">
          <h3 className="font-bold text-gray-900 mb-4 pb-2 border-b border-gray-200">
            About You
          </h3>
          
          <div>
            <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
              Bio
            </label>
            <textarea
              id="bio"
              name="bio"
              value={profileData.bio}
              onChange={handleChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-crimson-500 focus:border-transparent form-textarea"
              placeholder="Tell us about yourself, your achievements, and your journey since graduating from Palm Beach Lakes"
            />
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-2 bg-crimson-600 text-white rounded-md hover:bg-crimson-700 transition-colors flex items-center"
          >
            <FiSave className="w-4 h-4 mr-2" />
            Save Changes
          </button>
        </div>
      </form>
    </div>
  );
};

export default ProfileEditor; 