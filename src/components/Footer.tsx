import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON><PERSON>, FaLinkedinIn, FaEnvelope } from 'react-icons/fa';
import { motion } from 'framer-motion';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative overflow-hidden">
      {/* Top decorative wave */}
      <div className="absolute top-0 left-0 right-0 h-8 bg-beige-50">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="absolute -top-5 left-0 w-full text-beige-50 fill-current">
          <path fillOpacity="1" d="M0,192L48,186.7C96,181,192,171,288,181.3C384,192,480,224,576,218.7C672,213,768,171,864,170.7C960,171,1056,213,1152,224C1248,235,1344,213,1392,202.7L1440,192L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z"></path>
        </svg>
      </div>

      <div className="bg-gradient-to-b from-crimson-900 to-crimson-950 text-white pt-10 relative z-10">
        <div className="absolute inset-0 bg-[url('/images/texture.png')] opacity-5 mix-blend-overlay"></div>
        
        {/* Quote Banner */}
        <div className="relative w-full bg-gradient-to-r from-crimson-800 to-crimson-700 py-10 overflow-hidden">
          <div className="absolute inset-0 opacity-10">
            <div className="absolute -right-24 -top-24 w-64 h-64 rounded-full bg-white/20 blur-3xl"></div>
            <div className="absolute -left-24 -bottom-24 w-64 h-64 rounded-full bg-crimson-500/30 blur-3xl"></div>
          </div>
          <div className="container mx-auto max-w-5xl px-4 relative z-10">
            <div className="text-center max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
              >
                <span className="block mb-6 mx-auto w-20 h-1 bg-gradient-to-r from-gold-300 to-gold-500 rounded-full"></span>
                <h2 className="font-serif text-xl md:text-2xl italic font-normal text-white">
                  "Our greatest responsibility is to be good ancestors <br className="hidden md:block" />
                  to the generations that follow us."
                </h2>
                <p className="mt-3 text-white/80 font-light text-sm">— Alumni Legacy Statement</p>
              </motion.div>
            </div>
          </div>
        </div>
        
        {/* Spotlight and Stay Connected Banner */}
        <div className="relative bg-gradient-to-r from-crimson-800/80 to-crimson-800/60 py-10 backdrop-blur-sm">
          <div className="container mx-auto max-w-5xl px-4">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              {/* Alumni Spotlight */}
              <div className="text-center md:text-left">
                <h3 className="text-xl font-bold mb-3 text-white flex items-center justify-center md:justify-start">
                  <span className="mr-2">❤️</span> Alumni Spotlight
                </h3>
                <p className="text-white/80 mb-4">
                  Share your story with us for a chance to be featured in our alumni spotlight!
                </p>
                <Link 
                  to="/share-story"
                  className="inline-block px-6 py-2.5 rounded-lg bg-gradient-to-r from-gold-500 to-gold-600 text-black font-medium 
                            hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 shadow-md"
                >
                  Submit Your Journey
                </Link>
              </div>
              
              {/* Stay Connected */}
              <div className="text-center md:text-left">
                <h3 className="text-xl font-bold mb-3 text-white">Stay Connected</h3>
                <div className="flex max-w-md mx-auto md:mx-0">
                  <input
                    type="email"
                    placeholder="Your email address"
                    className="flex-grow px-4 py-3 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-gold-500/50 
                              bg-white/10 border border-white/20 text-white placeholder-white/60"
                  />
                  <button 
                    type="button"
                    className="px-5 py-3 rounded-r-lg bg-gradient-to-r from-gold-500 to-gold-600 text-black font-semibold 
                              hover:shadow-lg transition-all duration-300 hover:-translate-y-0.5 whitespace-nowrap"
                  >
                    Subscribe
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Main Footer */}
        <div className="container mx-auto max-w-5xl px-4 py-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            {/* Organization Info */}
            <div>
              <div className="flex items-center mb-4">
                <img 
                  src="/images/logo/pblnaa-logo.png" 
                  alt="PBLNAA Logo" 
                  className="w-14 h-14 mr-3" 
                />
                <div>
                  <h3 className="text-xl font-display font-bold text-white">Palm Beach Lakes</h3>
                  <h4 className="text-md font-serif text-white/90">National Alumni Association</h4>
                </div>
              </div>
              <p className="text-white/80 mb-6 leading-relaxed text-sm">
                Building bridges between successful alumni and aspiring students through mentorship,
                scholarships, and lifelong connections.
              </p>
              <div className="flex space-x-3 mt-4">
                <a 
                  href="https://facebook.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-9 h-9 rounded-full bg-white/10 flex items-center justify-center hover:bg-gradient-to-r hover:from-crimson-600 hover:to-crimson-700 
                            transition-all duration-300 hover:-translate-y-1 group"
                >
                  <FaFacebookF className="text-white text-sm group-hover:scale-110 transition-transform" />
                </a>
                <a 
                  href="https://twitter.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-9 h-9 rounded-full bg-white/10 flex items-center justify-center hover:bg-gradient-to-r hover:from-crimson-600 hover:to-crimson-700 
                            transition-all duration-300 hover:-translate-y-1 group"
                >
                  <FaTwitter className="text-white text-sm group-hover:scale-110 transition-transform" />
                </a>
                <a 
                  href="https://instagram.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-9 h-9 rounded-full bg-white/10 flex items-center justify-center hover:bg-gradient-to-r hover:from-crimson-600 hover:to-crimson-700 
                            transition-all duration-300 hover:-translate-y-1 group"
                >
                  <FaInstagram className="text-white text-sm group-hover:scale-110 transition-transform" />
                </a>
                <a 
                  href="https://linkedin.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="w-9 h-9 rounded-full bg-white/10 flex items-center justify-center hover:bg-gradient-to-r hover:from-crimson-600 hover:to-crimson-700 
                            transition-all duration-300 hover:-translate-y-1 group"
                >
                  <FaLinkedinIn className="text-white text-sm group-hover:scale-110 transition-transform" />
                </a>
              </div>
            </div>

            {/* Combined Links Column */}
            <div className="grid grid-cols-2 gap-8">
              {/* Quick Links */}
              <div>
                <Link to="/quick-links" className="text-base font-bold mb-4 text-white relative inline-block">
                  Quick Links
                  <span className="absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-gold-400 to-transparent rounded-full"></span>
                </Link>
                <ul className="space-y-3 text-sm">
                  <li>
                    <Link to="/about" className="text-white/80 hover:text-white flex items-center transition-colors duration-200 hover:translate-x-0.5">
                      <span className="w-1.5 h-1.5 bg-gradient-to-r from-gold-400 to-gold-500 rounded-full mr-2.5"></span>
                      About Us
                    </Link>
                  </li>
                  <li>
                    <Link to="/events" className="text-white/80 hover:text-white flex items-center transition-colors duration-200 hover:translate-x-0.5">
                      <span className="w-1.5 h-1.5 bg-gradient-to-r from-gold-400 to-gold-500 rounded-full mr-2.5"></span>
                      Events
                    </Link>
                  </li>
                  <li>
                    <Link to="/photo-gallery" className="text-white/80 hover:text-white flex items-center transition-colors duration-200 hover:translate-x-0.5">
                      <span className="w-1.5 h-1.5 bg-gradient-to-r from-gold-400 to-gold-500 rounded-full mr-2.5"></span>
                      Photo Gallery
                    </Link>
                  </li>
                  <li>
                    <Link to="/scholarships" className="text-white/80 hover:text-white flex items-center transition-colors duration-200 hover:translate-x-0.5">
                      <span className="w-1.5 h-1.5 bg-gradient-to-r from-gold-400 to-gold-500 rounded-full mr-2.5"></span>
                      Scholarships
                    </Link>
                  </li>
                  <li>
                    <Link to="/contact" className="text-white/80 hover:text-white flex items-center transition-colors duration-200 hover:translate-x-0.5">
                      <span className="w-1.5 h-1.5 bg-gradient-to-r from-gold-400 to-gold-500 rounded-full mr-2.5"></span>
                      Contact
                    </Link>
                  </li>
                </ul>
              </div>

              {/* Get Involved */}
              <div>
                <Link to="/get-involved" className="text-base font-bold mb-4 text-white relative inline-block">
                  Get Involved
                  <span className="absolute bottom-0 left-0 w-1/2 h-0.5 bg-gradient-to-r from-gold-400 to-transparent rounded-full"></span>
                </Link>
                <ul className="space-y-3 text-sm">
                  <li>
                    <Link to="/membership" className="text-white/80 hover:text-white flex items-center transition-colors duration-200 hover:translate-x-0.5">
                      <span className="w-1.5 h-1.5 bg-gradient-to-r from-gold-400 to-gold-500 rounded-full mr-2.5"></span>
                      Become a Member
                    </Link>
                  </li>
                  <li>
                    <Link to="/volunteer" className="text-white/80 hover:text-white flex items-center transition-colors duration-200 hover:translate-x-0.5">
                      <span className="w-1.5 h-1.5 bg-gradient-to-r from-gold-400 to-gold-500 rounded-full mr-2.5"></span>
                      Volunteer
                    </Link>
                  </li>
                  <li>
                    <Link to="/mentorship" className="text-white/80 hover:text-white flex items-center transition-colors duration-200 hover:translate-x-0.5">
                      <span className="w-1.5 h-1.5 bg-gradient-to-r from-gold-400 to-gold-500 rounded-full mr-2.5"></span>
                      Mentor a Student
                    </Link>
                  </li>
                </ul>
              </div>
            </div>

            {/* Contact */}
            <div>
              <Link to="/contact" className="text-base font-bold mb-4 text-white relative inline-block text-center w-full">
                Contact Us
                <span className="absolute bottom-0 left-1/4 right-1/4 w-1/2 h-0.5 bg-gradient-to-r from-gold-400 to-transparent rounded-full"></span>
              </Link>
              <ul className="space-y-4 mb-6 text-sm flex flex-col items-center">
                <li className="flex items-center justify-center hover:translate-y-0.5 transition-transform duration-200">
                  <span className="mr-3 text-gold-400">📞</span>
                  <a href="tel:+15612310514" className="text-white/80 hover:text-white transition-colors">
                    (*************
                  </a>
                </li>
                <li className="flex items-center justify-center">
                  <FaEnvelope className="w-4 h-4 mr-2.5 text-gold-400 flex-shrink-0" />
                  <a href="mailto:<EMAIL>" className="text-white/80 hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </li>
              </ul>
              
              <div className="p-4 bg-gradient-to-br from-crimson-700/40 to-crimson-900/40 rounded-lg border border-white/10 backdrop-blur-sm text-center">
                <p className="text-white/90 text-sm">
                  Looking to connect with fellow alumni? Join our alumni directory today!
                </p>
                <Link 
                  to="/directory" 
                  className="mt-3 inline-block text-gold-400 text-sm font-medium hover:text-gold-300 transition-colors"
                >
                  Explore Alumni Directory →
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/10 py-6 bg-crimson-950/50">
          <div className="container mx-auto max-w-5xl px-4">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-white/60 text-xs mb-4 md:mb-0">
                © {currentYear} Palm Beach Lakes National Alumni Association. All rights reserved.
              </p>
              <div className="flex flex-wrap justify-center gap-6">
                <Link to="/privacy" className="text-white/60 text-xs hover:text-white transition-colors">
                  Privacy Policy
                </Link>
                <Link to="/terms" className="text-white/60 text-xs hover:text-white transition-colors">
                  Terms of Service
                </Link>
                <Link to="/accessibility" className="text-white/60 text-xs hover:text-white transition-colors">
                  Accessibility
                </Link>
                <Link to="/sitemap" className="text-white/60 text-xs hover:text-white transition-colors">
                  Sitemap
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 